"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from "@/components/ui/accordion";
import { MessageCircle, ArrowRight } from "lucide-react";
import { faq } from "@/lib/constants";
import Link from "next/link";

export function FAQSection() {
  return (
    <section id="faq" className="py-24 bg-muted/20">
      <div className="container px-4 mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            FAQ
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            Frequently asked
            <span className="text-primary"> questions</span>
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
            Everything you need to know about TikPay. Can't find the answer you're 
            looking for? Please chat with our friendly team.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* FAQ Accordion */}
          <Card className="border-0 bg-background/50 backdrop-blur-sm mb-12">
            <CardContent className="p-8">
              <Accordion type="single" collapsible className="w-full">
                {faq.map((item) => (
                  <AccordionItem key={item.id} value={item.id} className="border-border/50">
                    <AccordionTrigger className="text-left hover:text-primary transition-colors">
                      <span className="text-lg font-semibold">{item.question}</span>
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground leading-relaxed pt-2">
                      {item.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>

          {/* Contact Support */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Still have questions */}
            <Card className="border-0 bg-gradient-to-br from-primary/10 to-blue-500/10 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mx-auto mb-6">
                  <MessageCircle className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-4">Still have questions?</h3>
                <p className="text-muted-foreground mb-6">
                  Can't find the answer you're looking for? Please chat with our friendly team.
                </p>
                <Button className="w-full" asChild>
                  <Link href="/support">
                    Get in Touch
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Documentation */}
            <Card className="border-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4">Developer Resources</h3>
                <p className="text-muted-foreground mb-6">
                  Comprehensive documentation, guides, and API references for developers.
                </p>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/docs">
                    View Documentation
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Quick Links */}
          <div className="mt-12 text-center">
            <h3 className="text-lg font-semibold mb-6">Popular Resources</h3>
            <div className="flex flex-wrap justify-center gap-4">
              {[
                { name: "API Documentation", href: "/docs/api" },
                { name: "Integration Guide", href: "/docs/integration" },
                { name: "Security Overview", href: "/security" },
                { name: "Pricing Calculator", href: "/pricing-calculator" },
                { name: "Status Page", href: "/status" },
                { name: "Contact Support", href: "/support" },
              ].map((link, index) => (
                <Button 
                  key={index} 
                  variant="ghost" 
                  size="sm" 
                  className="text-muted-foreground hover:text-primary"
                  asChild
                >
                  <Link href={link.href}>{link.name}</Link>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
