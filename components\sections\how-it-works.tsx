"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, ArrowDown } from "lucide-react";
import { howItWorks } from "@/lib/constants";
import Link from "next/link";

export function HowItWorksSection() {
  return (
    <section id="how-it-works" className="py-24 bg-gradient-to-b from-background to-muted/20">
      <div className="container px-4 mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            How it Works
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            Get started in
            <span className="text-primary"> 4 simple steps</span>
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
            From signup to your first payment in minutes. Our streamlined process 
            gets you up and running quickly.
          </p>
        </div>

        {/* Steps */}
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Connection Line */}
            <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-primary/20 via-primary/40 to-primary/20 transform -translate-x-1/2" />
            
            {howItWorks.map((step, index) => {
              const IconComponent = step.icon;
              const isEven = index % 2 === 0;
              
              return (
                <div key={step.id} className="relative mb-16 last:mb-0">
                  {/* Step Card */}
                  <div className={`lg:w-1/2 ${isEven ? 'lg:pr-12' : 'lg:pl-12 lg:ml-auto'}`}>
                    <Card className="group relative overflow-hidden border-0 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                      <CardContent className="p-8">
                        {/* Step Number */}
                        <div className="flex items-center gap-4 mb-6">
                          <div className="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-bold text-lg">
                            {step.step}
                          </div>
                          <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                            <IconComponent className="w-6 h-6 text-primary" />
                          </div>
                        </div>

                        {/* Content */}
                        <h3 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">
                          {step.title}
                        </h3>
                        <p className="text-muted-foreground leading-relaxed">
                          {step.description}
                        </p>

                        {/* Decorative Elements */}
                        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full transform translate-x-16 -translate-y-16" />
                      </CardContent>
                    </Card>
                  </div>

                  {/* Center Circle (Desktop) */}
                  <div className="hidden lg:block absolute left-1/2 top-8 w-6 h-6 bg-primary rounded-full transform -translate-x-1/2 border-4 border-background shadow-lg">
                    <div className="w-full h-full bg-primary rounded-full animate-ping opacity-20" />
                  </div>

                  {/* Arrow (Mobile) */}
                  {index < howItWorks.length - 1 && (
                    <div className="lg:hidden flex justify-center mt-8">
                      <ArrowDown className="w-6 h-6 text-primary animate-bounce" />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <Card className="inline-block border-0 bg-gradient-to-r from-primary/10 to-blue-500/10 backdrop-blur-sm">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4">Ready to start accepting payments?</h3>
              <p className="text-muted-foreground mb-6 max-w-md">
                Join thousands of businesses that trust TikPay for their payment processing needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="group" asChild>
                  <Link href="/auth/signup">
                    Get Started Now
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg">
                  Schedule Demo
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
