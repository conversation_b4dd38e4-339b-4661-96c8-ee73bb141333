@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Typography System */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Enhanced Breakpoints for Better Responsive Design */
  --breakpoint-xs: 30rem;    /* 480px - Mobile landscape */
  --breakpoint-sm: 40rem;    /* 640px - Tablet portrait */
  --breakpoint-md: 48rem;    /* 768px - Tablet landscape */
  --breakpoint-lg: 64rem;    /* 1024px - Desktop */
  --breakpoint-xl: 80rem;    /* 1280px - Large desktop */
  --breakpoint-2xl: 96rem;   /* 1536px - Extra large */
  --breakpoint-3xl: 120rem;  /* 1920px - Ultra wide */

  /* Container Queries for Component-Based Responsive Design */
  --container-xs: 20rem;     /* 320px */
  --container-sm: 24rem;     /* 384px */
  --container-md: 28rem;     /* 448px */
  --container-lg: 32rem;     /* 512px */
  --container-xl: 36rem;     /* 576px */
  --container-2xl: 42rem;    /* 672px */

  /* Enhanced Spacing Scale */
  --spacing-xs: 0.125rem;    /* 2px */
  --spacing-sm: 0.25rem;     /* 4px */
  --spacing-md: 0.5rem;      /* 8px */
  --spacing-lg: 1rem;        /* 16px */
  --spacing-xl: 1.5rem;      /* 24px */
  --spacing-2xl: 2rem;       /* 32px */
  --spacing-3xl: 3rem;       /* 48px */

  /* Animation Easing Functions */
  --ease-in-out-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in-out-snappy: cubic-bezier(0.2, 0, 0, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Color System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* Improved font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Enhanced focus styles for accessibility */
  :focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }

  /* Smooth scrolling for better UX */
  html {
    scroll-behavior: smooth;
  }

  /* Reduced motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

@layer components {

  /* Authentication Layout Components */
  .auth-container {
    @apply min-h-screen grid lg:grid-cols-2;
  }

  .auth-branding {
    @apply hidden lg:flex lg:flex-col lg:justify-center lg:px-12 bg-muted;
  }

  .auth-form-container {
    @apply flex flex-col justify-center px-4 py-12 sm:px-6 lg:px-8;
  }

  .auth-form-wrapper {
    @apply mx-auto w-full max-w-sm;
  }

  /* Card Components with Container Queries */
  .card-responsive {
    @apply @container rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-content-responsive {
    @apply p-6 @sm:p-8 @md:p-10;
  }

  /* Button Enhancements */
  .btn-loading {
    @apply relative disabled:pointer-events-none;
  }

  .btn-loading::after {
    content: "";
    @apply absolute inset-0 rounded-[inherit] bg-current opacity-20;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Form Components */
  .form-field-container {
    @apply space-y-2;
  }

  .form-input-with-icon {
    @apply relative;
  }

  .form-input-icon {
    @apply absolute left-3 top-3 h-4 w-4 text-muted-foreground;
  }

  .form-input-with-icon input {
    @apply pl-10;
  }

  /* Landing Page Components - Enhanced Spacing System */
  .hero-section {
    @apply container py-24 md:py-32 lg:py-40;
  }

  .hero-content {
    @apply mx-auto max-w-6xl text-center;
  }

  .feature-grid {
    @apply grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3;
  }

  /* Section Spacing System - 8pt Grid */
  .section-container {
    @apply container px-4 mx-auto;
  }

  .section-spacing {
    @apply py-16 md:py-24 lg:py-32;
  }

  .section-header {
    @apply text-center mb-12 md:mb-16 lg:mb-20;
  }

  .section-content-narrow {
    @apply max-w-4xl mx-auto;
  }

  .section-content-wide {
    @apply max-w-6xl mx-auto;
  }

  .section-content-full {
    @apply max-w-7xl mx-auto;
  }

  /* Grid System Consistency */
  .grid-2-cols {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8;
  }

  .grid-3-cols {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8;
  }

  .grid-4-cols {
    @apply grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8;
  }

  /* Typography System */
  .heading-hero {
    @apply text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight;
  }

  .heading-section {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight;
  }

  .heading-card {
    @apply text-xl md:text-2xl font-bold;
  }

  .text-lead {
    @apply text-lg md:text-xl lg:text-2xl text-muted-foreground leading-relaxed;
  }

  .text-body {
    @apply text-base md:text-lg text-muted-foreground leading-relaxed;
  }

  /* Enhanced Card Components */
  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-xl;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-background to-muted/50;
  }

  .card-glass {
    @apply bg-background/50 backdrop-blur-sm border-0;
  }



  /* Button Enhancements */
  .btn-cta {
    @apply px-8 py-6 text-lg font-semibold transition-all hover:scale-105;
  }

  .btn-icon-right {
    @apply group;
  }

  .btn-icon-right .icon {
    @apply ml-2 h-4 w-4 transition-transform group-hover:translate-x-1;
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 1000ms ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 1000ms ease-out forwards;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Utility Classes for Performance */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-scroll {
    will-change: scroll-position;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}
