"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Quote } from "lucide-react";
import { testimonials } from "@/lib/constants";

export function TestimonialsSection() {
  return (
    <section id="testimonials" className="section-spacing bg-gradient-to-b from-background to-muted/20">
      <div className="section-container">
        {/* Section Header */}
        <div className="section-header">
          <Badge variant="outline" className="mb-4">
            Testimonials
          </Badge>
          <h2 className="heading-section mb-6">
            Loved by businesses
            <span className="text-primary"> worldwide</span>
          </h2>
          <p className="text-lead min-w-3xl mx-auto">
            Don't just take our word for it. Here's what our customers have to say
            about their experience with TikPay.
          </p>
        </div>

        {/* Stats */}
        <div className="grid-4-cols mb-16 section-content-narrow">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">500K+</div>
            <div className="text-sm text-muted-foreground">Happy Customers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">4.9/5</div>
            <div className="text-sm text-muted-foreground">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">99.9%</div>
            <div className="text-sm text-muted-foreground">Customer Satisfaction</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-primary mb-2">150+</div>
            <div className="text-sm text-muted-foreground">Countries Served</div>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid-3-cols section-content-wide">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className="relative overflow-hidden border-0 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              <CardContent className="p-8 relative z-10">
                {/* Quote Icon */}
                <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6 hover:bg-primary/20 transition-colors">
                  <Quote className="w-6 h-6 text-primary" />
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Content */}
                <blockquote className="text-muted-foreground leading-relaxed mb-6">
                  "{testimonial.content}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 flex items-center justify-center">
                    <span className="text-sm font-semibold text-primary">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.position}</div>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/5 to-transparent rounded-full transform translate-x-16 -translate-y-16 hover:scale-150 transition-transform duration-500" />
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/5 to-transparent rounded-full transform -translate-x-12 translate-y-12 hover:scale-150 transition-transform duration-500" />
              </CardContent>

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300" />
            </Card>
          ))}
        </div>

        {/* Additional Testimonials Carousel Placeholder */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 text-sm text-muted-foreground">
            <div className="flex gap-1">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              ))}
            </div>
            <span>Rated 4.9/5 by 10,000+ customers</span>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16">
          <div className="text-center mb-8">
            <h3 className="text-xl font-semibold mb-4">Trusted by industry leaders</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto opacity-60">
            {['TechCorp', 'InnovateLab', 'GlobalTrade', 'StartupHub'].map((company, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-lg flex items-center justify-center">
                  <span className="text-sm font-semibold text-primary">
                    {company.substring(0, 2)}
                  </span>
                </div>
                <div className="text-sm font-medium text-muted-foreground">{company}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
