"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Menu, 
  X, 
  ChevronDown,
  ArrowRight,
  Zap,
  BarChart3,
  CreditCard,
  Users
} from "lucide-react";
import { navigation } from "@/lib/constants";

export function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const productDropdownItems = [
    {
      icon: CreditCard,
      title: "Payment Processing",
      description: "Accept payments globally",
      href: "/products/payments"
    },
    {
      icon: BarChart3,
      title: "Analytics",
      description: "Real-time insights",
      href: "/products/analytics"
    },
    {
      icon: Shield,
      title: "Security",
      description: "Enterprise-grade protection",
      href: "/products/security"
    },
    {
      icon: Zap,
      title: "APIs",
      description: "Developer-friendly tools",
      href: "/products/apis"
    }
  ];

  const resourcesDropdownItems = [
    { title: "Documentation", href: "/docs" },
    { title: "API Reference", href: "/docs/api" },
    { title: "Guides", href: "/guides" },
    { title: "Blog", href: "/blog" },
    { title: "Case Studies", href: "/case-studies" },
    { title: "Support", href: "/support" }
  ];

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-background/80 backdrop-blur-md border-b shadow-sm' 
          : 'bg-transparent'
      }`}
    >
      <div className="container px-4 mx-auto">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <Shield className="h-8 w-8 text-primary group-hover:scale-110 transition-transform" />
            <span className="text-xl font-bold">TikPay</span>
            <Badge variant="outline" className="ml-2 text-xs bg-primary/10 border-primary/20">
              v2.0
            </Badge>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {/* Products Dropdown */}
            <div 
              className="relative"
              onMouseEnter={() => setActiveDropdown('products')}
              onMouseLeave={() => setActiveDropdown(null)}
            >
              <button className="flex items-center space-x-1 text-muted-foreground hover:text-primary transition-colors">
                <span>Products</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              
              {activeDropdown === 'products' && (
                <div className="absolute top-full left-0 mt-2 w-80 bg-background/95 backdrop-blur-md border rounded-lg shadow-xl p-4">
                  <div className="grid grid-cols-1 gap-3">
                    {productDropdownItems.map((item, index) => {
                      const IconComponent = item.icon;
                      return (
                        <Link
                          key={index}
                          href={item.href}
                          className="flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                        >
                          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                            <IconComponent className="w-5 h-5 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium text-foreground group-hover:text-primary transition-colors">
                              {item.title}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {item.description}
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Regular Navigation Items */}
            {navigation.map((item) => (
              <Link
                key={item.id}
                href={item.url}
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                {item.title}
              </Link>
            ))}

            {/* Resources Dropdown */}
            <div 
              className="relative"
              onMouseEnter={() => setActiveDropdown('resources')}
              onMouseLeave={() => setActiveDropdown(null)}
            >
              <button className="flex items-center space-x-1 text-muted-foreground hover:text-primary transition-colors">
                <span>Resources</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              
              {activeDropdown === 'resources' && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-background/95 backdrop-blur-md border rounded-lg shadow-xl p-2">
                  {resourcesDropdownItems.map((item, index) => (
                    <Link
                      key={index}
                      href={item.href}
                      className="block px-3 py-2 text-sm text-muted-foreground hover:text-primary hover:bg-muted/50 rounded-md transition-colors"
                    >
                      {item.title}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </nav>

          {/* Desktop CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/auth/signin">Sign In</Link>
            </Button>
            <Button className="group" asChild>
              <Link href="/auth/signup">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md border-b shadow-xl">
            <div className="p-4 space-y-4">
              {/* Mobile Navigation Items */}
              {navigation.map((item) => (
                <Link
                  key={item.id}
                  href={item.url}
                  className="block py-2 text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.title}
                </Link>
              ))}
              
              {/* Mobile Product Links */}
              <div className="pt-4 border-t">
                <div className="text-sm font-medium text-foreground mb-3">Products</div>
                {productDropdownItems.map((item, index) => (
                  <Link
                    key={index}
                    href={item.href}
                    className="block py-2 text-sm text-muted-foreground hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.title}
                  </Link>
                ))}
              </div>

              {/* Mobile Resource Links */}
              <div className="pt-4 border-t">
                <div className="text-sm font-medium text-foreground mb-3">Resources</div>
                {resourcesDropdownItems.map((item, index) => (
                  <Link
                    key={index}
                    href={item.href}
                    className="block py-2 text-sm text-muted-foreground hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.title}
                  </Link>
                ))}
              </div>

              {/* Mobile CTA Buttons */}
              <div className="pt-4 border-t space-y-3">
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/auth/signin" onClick={() => setIsMobileMenuOpen(false)}>
                    Sign In
                  </Link>
                </Button>
                <Button className="w-full group" asChild>
                  <Link href="/auth/signup" onClick={() => setIsMobileMenuOpen(false)}>
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
